<!DOCTYPE html>
<html>
<head>
    <title>JavaScript语法测试</title>
</head>
<body>
    <h1>JavaScript语法测试</h1>
    <div id="result"></div>
    
    <script>
        // 测试修复后的代码片段
        try {
            // 模拟供应商数据
            const supplier = {
                id: 1,
                name: "测试供应商",
                supplier_type: "金料供应商",
                contact_person: "张三",
                phone: "13800138000",
                owed_amount: 1000,
                owed_gold: 50,
                deposit_amount: 500,
                deposit_gold: 25
            };
            
            // 测试修复后的HTML生成代码
            var html = '';
            html += '<div class="search-result-item" data-supplier-id="' + supplier.id + '" ' +
                   'style="padding: 6px 12px; cursor: pointer; border-bottom: 1px solid #eee; background: white; font-size: 14px; line-height: 1.4;" ' +
                   'onmouseover="this.style.backgroundColor=\'#f8f9fa\'" onmouseout="this.style.backgroundColor=\'white\'" ' +
                   'data-info="' + encodeURIComponent(JSON.stringify({
                       name: supplier.name,
                       supplier_type: supplier.supplier_type,
                       contact_person: supplier.contact_person,
                       phone: supplier.phone,
                       owed_amount: supplier.owed_amount || 0,
                       owed_gold: supplier.owed_gold || 0,
                       deposit_amount: supplier.deposit_amount || 0,
                       deposit_gold: supplier.deposit_gold || 0
                   })) + '">' +
                   supplier.name +
                   '</div>';
            
            // 测试Promise.all语法
            Promise.all([
                Promise.resolve("test1"),
                Promise.resolve("test2"),
                Promise.resolve("test3")
            ]).then(() => {
                console.log("Promise.all执行成功");
            }).catch(error => {
                console.log("Promise.all执行失败:", error);
            });
            
            document.getElementById('result').innerHTML = '<p style="color: green;">✅ JavaScript语法测试通过！</p>' + 
                '<p>生成的HTML:</p><div style="border: 1px solid #ccc; padding: 10px;">' + html + '</div>';
                
        } catch (error) {
            document.getElementById('result').innerHTML = '<p style="color: red;">❌ JavaScript语法错误: ' + error.message + '</p>';
        }
    </script>
</body>
</html>
